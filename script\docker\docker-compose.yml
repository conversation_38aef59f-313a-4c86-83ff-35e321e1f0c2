version: '3'
services:
  nacos:
    image: nacos/nacos-server:v2.3.2
    hostname: "nacos-standalone"
    environment:
      - NACOS_AUTH_ENABLE=true
      - NACOS_AUTH_CACHE_ENABLE=true
      - NACOS_AUTH_IDENTITY_KEY=nacos
      - NACOS_AUTH_IDENTITY_VALUE=nacos
      - NACOS_AUTH_TOKEN= # 请阅读官方文档了解规则后替换为自己的token：https://nacos.io/zh-cn/docs/v2/guide/user/auth.html
      - MODE=standalone
      - TZ=Asia/Shanghai
    volumes:
      - /docker/nacos/standalone-logs/:/home/<USER>/logs
      - /docker/nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - 8848:8848
      - 9848:9848
      - 9849:9849
    networks:
      blade_net:
        ipv4_address: ***********

  sentinel:
    image: bladex/sentinel-dashboard:1.8.0
    hostname: "sentinel"
    ports:
      - 8858:8858
    restart: on-failure
    networks:
      blade_net:
        ipv4_address: ***********

  blade-nginx:
    image: nginx:stable-alpine-perl
    hostname: "blade-nginx"
    ports:
    - 88:88
    volumes:
    - /docker/nginx/api/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
    - blade_net

  web-nginx:
    image: nginx:stable-alpine-perl
    hostname: "web-nginx"
    ports:
      - 8000:8000
    volumes:
      - /docker/nginx/web/html:/usr/share/nginx/html
      - /docker/nginx/web/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-redis:
    image: redis:5.0.2-alpine
    hostname: "blade-redis"
    ports:
    - 3379:6379
    volumes:
    - /docker/redis/data:/data
    command: "redis-server --appendonly yes"
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-admin:
    image: "${REGISTER}/blade-admin:${TAG}"
    ports:
    - 7002:7002
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-gateway1:
    image: "${REGISTER}/blade-gateway:${TAG}"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-gateway2:
    image: "${REGISTER}/blade-gateway:${TAG}"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-auth1:
    image: "${REGISTER}/blade-auth:${TAG}"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-auth2:
    image: "${REGISTER}/blade-auth:${TAG}"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-report:
    image: "${REGISTER}/blade-report:${TAG}"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-log:
    image: "${REGISTER}/blade-log:${TAG}"
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-desk:
    image: "${REGISTER}/blade-desk:${TAG}"
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-system:
    image: "${REGISTER}/blade-system:${TAG}"
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-resource:
    image: "${REGISTER}/blade-resource:${TAG}"
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-develop:
    image: "${REGISTER}/blade-develop:${TAG}"
    privileged: true
    restart: always
    networks:
      - blade_net

networks:
  blade_net:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
