#服务器配置
server:
  undertow:
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    # 线程配置
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400

#spring配置
spring:
  cloud:
    sentinel:
      eager: true
  devtools:
    restart:
      log-condition-evaluation-delta: false
    livereload:
      port: 23333

#feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

#对外暴露端口
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

#springdoc-openapi配置
springdoc:
  default-flat-param-object: true

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: blade
    password: blade
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh_cn
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2025 SpringBlade All Rights Reserved

#swagger配置信息
swagger:
  title: SpringBlade 接口文档系统
  description: SpringBlade 接口文档系统
  version: 4.6.0
  license: Powered By SpringBlade
  licenseUrl: https://bladex.cn
  terms-of-service-url: https://bladex.cn
  contact:
    name: smallchill
    email: <EMAIL>
    url: https://gitee.com/smallc

#blade配置
blade:
  auth:
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2验签,需和前端保持一致
    public-key: ${BLADE_OAUTH2_PUBLIC_KEY}
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2解密,前端无需配置
    private-key: ${BLADE_OAUTH2_PRIVATE_KEY}
  token:
    #使用 @org.springblade.test.SignKeyGenerator 获取
    sign-key: ${BLADE_TOKEN_SIGN_KEY}
    #使用 @org.springblade.test.SignKeyGenerator 获取
    aes-key: ${BLADE_TOKEN_CRYPTO_KEY}
  xss:
    enabled: true
    skip-url:
      - /weixin
  secure:
    skip-url:
      - /test/**
    client:
      - client-id: sword
        path-patterns:
          - /sword/**
      - client-id: saber
        path-patterns:
          - /saber/**
  tenant:
    column: tenant_id
    tables:
      - blade_notice

