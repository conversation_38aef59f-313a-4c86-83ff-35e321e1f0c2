<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleResultMap" type="org.springblade.system.entity.Role">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="role_name" property="roleName"/>
        <result column="sort" property="sort"/>
        <result column="role_alias" property="roleAlias"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select
        id, parent_id, role_name, sort, role_alias, is_deleted
    </sql>

    <select id="selectRolePage" resultMap="roleResultMap">
        select * from blade_role where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, role_name as title, id as 'value', id as 'key' from blade_role where is_deleted = 0
        <if test="param1!=null">
            and tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and role_alias &lt;&gt; #{param2}
        </if>
    </select>

    <select id="getRoleNames" resultType="java.lang.String">
        SELECT
        role_name
        FROM
        blade_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>
